#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查默认算法代码
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import sessionmaker
from app.database import engine
from app.models.activation_algorithm import ActivationAlgorithm
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_default_algorithm():
    """检查默认算法代码"""
    try:
        # 创建会话
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()
        
        try:
            # 查找默认算法
            default_algorithm = db.query(ActivationAlgorithm).filter(
                ActivationAlgorithm.is_default == True
            ).first()
            
            if not default_algorithm:
                logger.error("未找到默认算法")
                return False
            
            print(f"默认算法信息:")
            print(f"- ID: {default_algorithm.id}")
            print(f"- 名称: {default_algorithm.name}")
            print(f"- 版本: {default_algorithm.version}")
            print(f"- 作者: {default_algorithm.author}")
            print(f"- 状态: {'启用' if default_algorithm.is_active else '禁用'}")
            print(f"- 是否默认: {default_algorithm.is_default}")
            print(f"\n算法代码:")
            print("-" * 80)
            print(default_algorithm.algorithm_code)
            print("-" * 80)
            
            return True
            
        except Exception as e:
            logger.error(f"检查默认算法失败: {str(e)}")
            return False
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"数据库操作失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = check_default_algorithm()
    if success:
        print("\n✅ 默认算法检查完成！")
    else:
        print("\n❌ 默认算法检查失败！")
        sys.exit(1)
