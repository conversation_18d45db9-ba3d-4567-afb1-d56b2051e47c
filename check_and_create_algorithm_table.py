#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查并创建算法管理表
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from app.config import settings
from app.database import Base
from app.models.activation_algorithm import ActivationAlgorithm
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_and_create_table():
    """检查并创建算法管理表"""
    try:
        # 创建数据库引擎
        engine = create_engine(
            settings.database_url,
            echo=True,
            pool_pre_ping=True,
            pool_recycle=300
        )
        
        # 检查表是否存在
        with engine.connect() as conn:
            result = conn.execute(text("SHOW TABLES LIKE 'activation_algorithms'"))
            table_exists = result.fetchone() is not None
            
            if table_exists:
                logger.info("表 'activation_algorithms' 已存在")
            else:
                logger.info("表 'activation_algorithms' 不存在，正在创建...")
                
                # 创建表
                Base.metadata.create_all(bind=engine)
                logger.info("表创建成功！")
        
        # 创建会话并插入默认算法
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()
        
        try:
            # 检查是否已存在默认算法
            existing = db.query(ActivationAlgorithm).filter(
                ActivationAlgorithm.name == "HMAC-SHA256 默认算法"
            ).first()
            
            if existing:
                logger.info("默认算法已存在")
                return True
            
            # 默认算法代码
            default_algorithm_code = '''# HMAC-SHA256 激活码生成算法
# 这是系统默认使用的算法，基于HMAC-SHA256和Base64编码

import hashlib
import hmac
import time
import base64

# 使用与原系统相同的密钥
master_key = "FocuSee_Master_2025_Secret_Key_V1"
hmac_key = hashlib.sha256((master_key + "_HMAC").encode()).digest()[:32]

# 生成时间戳
timestamp = int(time.time())

# 构建数据字符串
data_string = f"{machine_id}|{days}|{timestamp}"

# 生成HMAC签名
signature = hmac.new(hmac_key, data_string.encode(), hashlib.sha256).digest()

# 组合数据和签名
combined_data = data_string.encode() + b"|" + signature

# Base64编码
encoded = base64.b64encode(combined_data).decode()

# 格式化激活码（每4个字符用-分隔）
formatted_code = "-".join([encoded[i:i+4] for i in range(0, len(encoded), 4)])

# 设置激活码
activation_code = formatted_code'''
            
            # 创建默认算法
            default_algorithm = ActivationAlgorithm(
                name="HMAC-SHA256 默认算法",
                description="系统默认使用的HMAC-SHA256激活码生成算法，提供高安全性和兼容性",
                algorithm_code=default_algorithm_code,
                is_active=True,
                is_default=True,
                version="1.0",
                author="FocuSee System"
            )
            
            db.add(default_algorithm)
            db.commit()
            
            logger.info("默认算法创建成功！")
            return True
            
        except Exception as e:
            logger.error(f"创建默认算法失败: {str(e)}")
            db.rollback()
            return False
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"数据库操作失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = check_and_create_table()
    if success:
        print("✅ 算法管理表检查和初始化完成！")
    else:
        print("❌ 算法管理表初始化失败！")
        sys.exit(1)
