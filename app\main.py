from fastapi import <PERSON><PERSON><PERSON>, Request, Depends, HTTPException, Form
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session
from app.config import settings
from app.database import get_db, create_tables
from app.api import auth, download, admin, products, orders, agents, licenses, agent_auth, agent_product_auth, user_auth, agent_dashboard, agent_licenses, agent_orders, admin_profile, machine_activation, activation_algorithm
from app.models import User, DownloadLog, AdminUser
from app.services.admin_service import AdminService
from app.utils.logger import setup_logging
from app.utils.exceptions import (
    FocuSeeException,
    focusee_exception_handler,
    general_exception_handler
)
import os
import logging

# 配置日志
logger = setup_logging()

# 创建FastAPI应用
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="FocuSee Registration and Download Management System",
    docs_url=settings.docs_url,
    redoc_url=settings.redoc_url
)

# 添加异常处理器
app.add_exception_handler(FocuSeeException, focusee_exception_handler)
app.add_exception_handler(Exception, general_exception_handler)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 配置模板
templates = Jinja2Templates(directory="app/templates")

# 创建必要的目录
os.makedirs(settings.downloads_dir, exist_ok=True)
os.makedirs("scripts", exist_ok=True)

# 挂载静态文件目录
app.mount("/scripts", StaticFiles(directory="scripts"), name="scripts")
app.mount("/downloads", StaticFiles(directory="downloads"), name="downloads")
# 挂载静态资源目录（用于首页的CSS、JS、图片等）
app.mount("/static", StaticFiles(directory="static"), name="static")

# 包含API路由
app.include_router(auth.router, prefix="/api/auth", tags=["Authentication"])
app.include_router(download.router, tags=["Download"])
app.include_router(admin.router, prefix="/api", tags=["Admin"])
app.include_router(products.router, prefix="/api/products", tags=["Products"])
app.include_router(orders.router, prefix="/api/orders", tags=["Orders"])
app.include_router(agents.router, prefix="/api/agents", tags=["Agents"])
app.include_router(licenses.router, prefix="/api/licenses", tags=["Licenses"])
app.include_router(agent_auth.router, prefix="/api/agent-auth", tags=["Agent Auth"])
app.include_router(agent_product_auth.router, prefix="/api/agent-product-auth", tags=["Agent Product Auth"])
app.include_router(agent_dashboard.router, prefix="/api/agent/dashboard", tags=["Agent Dashboard"])
app.include_router(agent_licenses.router, prefix="/api/agent/licenses", tags=["Agent Licenses"])
app.include_router(agent_orders.router, prefix="/api/agent/orders", tags=["Agent Orders"])
app.include_router(user_auth.router, prefix="/api/user-auth", tags=["User Auth"])
app.include_router(admin_profile.router, prefix="/api/admin", tags=["Admin Profile"])
app.include_router(machine_activation.router, prefix="/api/machine-activation", tags=["Machine Activation"])
app.include_router(activation_algorithm.router, prefix="/api/activation-algorithm", tags=["Activation Algorithm"])

@app.on_event("startup")
async def startup_event():
    """应用启动时的初始化"""
    logger.info("Starting FocuSee Registration System...")
    logger.info("Database tables should be initialized using: python init_db.py")

@app.get("/", response_class=HTMLResponse)
async def root(request: Request):
    """首页 - FocuSee 产品营销页面"""
    return templates.TemplateResponse("index.html", {"request": request})

@app.get("/admin", response_class=HTMLResponse)
async def admin_root(request: Request):
    """管理员入口 - 管理员登录页面"""
    return templates.TemplateResponse("admin/login.html", {"request": request})

@app.get("/admin/products", response_class=HTMLResponse)
async def admin_products(request: Request, db: Session = Depends(get_db)):
    """产品管理页面"""
    try:
        from app.models.product import Product
        products = db.query(Product).all()
        return templates.TemplateResponse("admin/products.html", {
            "request": request,
            "products": products
        })
    except Exception as e:
        logger.error(f"Error in admin products: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.get("/admin/orders", response_class=HTMLResponse)
async def admin_orders(request: Request, db: Session = Depends(get_db)):
    """订单管理页面"""
    try:
        from app.models.order import Order
        from app.models.product import Product
        from app.models.agent import Agent
        
        orders = db.query(Order).all()
        products = db.query(Product).filter(Product.is_active == True).all()
        agents = db.query(Agent).filter(Agent.is_active == True).all()
        
        return templates.TemplateResponse("admin/orders.html", {
            "request": request,
            "orders": orders,
            "products": products,
            "agents": agents
        })
    except Exception as e:
        logger.error(f"Error in admin orders: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.get("/admin/licenses", response_class=HTMLResponse)
async def admin_licenses(request: Request, db: Session = Depends(get_db)):
    """授权码管理页面"""
    try:
        from app.models.license import License
        from app.models.product import Product
        from app.models.agent import Agent
        from app.models.order import Order

        from app.models.license import LicenseStatus
        licenses = db.query(License).filter(License.status != LicenseStatus.REVOKED).all()
        products = db.query(Product).filter(Product.is_active == True).all()
        agents = db.query(Agent).filter(Agent.is_active == True).all()
        orders = db.query(Order).all()

        return templates.TemplateResponse("admin/licenses.html", {
            "request": request,
            "licenses": licenses,
            "products": products,
            "agents": agents,
            "orders": orders
        })
    except Exception as e:
        logger.error(f"Error in admin licenses: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.get("/admin/products/{product_id}/downloads", response_class=HTMLResponse)
async def admin_product_downloads(request: Request, product_id: int, db: Session = Depends(get_db)):
    """产品下载管理页面"""
    try:
        from app.models.product import Product
        from app.models.download_log import DownloadLog
        from app.models.license import License

        # 获取产品信息
        product = db.query(Product).filter(Product.id == product_id).first()
        if not product:
            raise HTTPException(status_code=404, detail="Product not found")

        # 通过License表关联获取该产品的下载日志
        # 首先获取该产品的所有授权码
        product_licenses = db.query(License).filter(License.product_id == product_id).all()
        license_codes = [license.license_code for license in product_licenses]

        # 然后获取这些授权码的下载日志
        download_logs = []
        if license_codes:
            download_logs = db.query(DownloadLog).filter(
                DownloadLog.license_key.in_(license_codes)
            ).order_by(DownloadLog.download_time.desc()).all()

        return templates.TemplateResponse("admin/downloads.html", {
            "request": request,
            "product": product,
            "download_logs": download_logs
        })
    except Exception as e:
        logger.error(f"Error in admin product downloads: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

# 代理商页面路由
@app.get("/agent/login", response_class=HTMLResponse)
async def agent_login(request: Request):
    """代理商登录页面"""
    return templates.TemplateResponse("agent/login.html", {"request": request})

@app.get("/agent/dashboard", response_class=HTMLResponse)
async def agent_dashboard(request: Request):
    """代理商仪表板页面"""
    try:
        return templates.TemplateResponse("agent/dashboard_simple.html", {"request": request})
    except Exception as e:
        logger.error(f"Error in agent dashboard: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.get("/agent/licenses", response_class=HTMLResponse)
async def agent_licenses(request: Request):
    """代理商授权码管理页面"""
    return templates.TemplateResponse("agent/licenses.html", {"request": request})

@app.get("/agent/products", response_class=HTMLResponse)
async def agent_products(request: Request):
    """代理商产品页面"""
    return templates.TemplateResponse("agent/products.html", {"request": request})

@app.get("/agent/profile", response_class=HTMLResponse)
async def agent_profile(request: Request):
    """代理商个人设置页面"""
    return templates.TemplateResponse("agent/profile.html", {"request": request})

@app.get("/agent/orders", response_class=HTMLResponse)
async def agent_orders(request: Request):
    """代理商订单管理页面"""
    return templates.TemplateResponse("agent/orders.html", {"request": request})

@app.get("/admin/agents", response_class=HTMLResponse)
async def admin_agents(request: Request, db: Session = Depends(get_db)):
    """代理商管理页面"""
    try:
        from app.models.agent import Agent
        agents = db.query(Agent).all()
        total = len(agents)
        return templates.TemplateResponse("admin/agents.html", {
            "request": request,
            "agents": agents,
            "total": total
        })
    except Exception as e:
        logger.error(f"Error in admin agents: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

# 用户页面路由
@app.get("/user/login", response_class=HTMLResponse)
async def user_login(request: Request):
    """用户登录页面"""
    return templates.TemplateResponse("user/login.html", {"request": request})

@app.get("/user/dashboard", response_class=HTMLResponse)
async def user_dashboard(request: Request):
    """用户仪表板页面"""
    return templates.TemplateResponse("user/dashboard.html", {"request": request})

@app.get("/admin/agents/{agent_id}/products", response_class=HTMLResponse)
async def admin_agent_products(request: Request, agent_id: int, db: Session = Depends(get_db)):
    """代理商产品授权页面"""
    try:
        from app.services.agent_service import AgentService
        from app.services.product_service import ProductService
        from app.services.agent_product_auth_service import AgentProductAuthService

        # 获取代理商信息
        agent = AgentService.get_agent_by_id(db, agent_id)
        if not agent:
            raise HTTPException(status_code=404, detail="Agent not found")

        # 获取所有产品
        products = ProductService.get_products(db, 0, 1000, True)

        # 获取代理商的产品授权
        agent_auths = AgentProductAuthService.get_agent_product_auths(db, agent_id)

        return templates.TemplateResponse("admin/agent_products.html", {
            "request": request,
            "agent": agent,
            "products": products,
            "agent_auths": agent_auths
        })
    except Exception as e:
        logger.error(f"Error in admin agent products: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.get("/admin/system", response_class=HTMLResponse)
async def admin_system(request: Request, db: Session = Depends(get_db)):
    """系统监控页面"""
    try:
        import psutil
        from datetime import datetime, timedelta
        from app.models.user import User
        from app.models.agent import Agent
        from app.models.license import License
        from app.models.api_call_log import ApiCallLog
        from app.models.download_log import DownloadLog
        from sqlalchemy import func
        
        # 系统运行时间
        boot_time = datetime.fromtimestamp(psutil.boot_time())
        uptime = datetime.now() - boot_time
        uptime_str = f"{uptime.days}天 {uptime.seconds // 3600}小时 {(uptime.seconds // 60) % 60}分钟"
        
        # 统计数据
        total_users = db.query(User).count()
        total_agents = db.query(Agent).count()
        total_licenses = db.query(License).count()
        active_licenses = db.query(License).filter(License.status == 'active').count()
        
        # API调用统计
        total_api_calls = db.query(ApiCallLog).count()
        today = datetime.now().date()
        today_api_calls = db.query(ApiCallLog).filter(
            func.date(ApiCallLog.call_time) == today
        ).count()

        # 下载统计
        total_downloads = db.query(DownloadLog).count()
        today_downloads = db.query(DownloadLog).filter(
            func.date(DownloadLog.download_time) == today
        ).count()

        # 错误率计算
        error_calls = db.query(ApiCallLog).filter(ApiCallLog.response_status >= 400).count()
        error_rate = round((error_calls / total_api_calls * 100) if total_api_calls > 0 else 0, 2)

        # 系统资源使用率
        memory_info = psutil.virtual_memory()
        memory_usage = round(memory_info.percent, 2)
        memory_total = round(memory_info.total / (1024**3), 2)  # GB
        memory_used = round(memory_info.used / (1024**3), 2)   # GB

        cpu_usage = round(psutil.cpu_percent(interval=1), 2)
        cpu_count = psutil.cpu_count()

        disk_info = psutil.disk_usage('/')
        disk_usage = round(disk_info.percent, 2)
        disk_total = round(disk_info.total / (1024**3), 2)  # GB
        disk_used = round(disk_info.used / (1024**3), 2)    # GB

        # 最近7天的API调用趋势
        api_trend = []
        for i in range(7):
            date = (datetime.now() - timedelta(days=6-i)).date()
            count = db.query(ApiCallLog).filter(
                func.date(ApiCallLog.call_time) == date
            ).count()
            api_trend.append({
                "date": date.strftime("%m-%d"),
                "count": count
            })
        
        # 日志数据
        recent_logs = []
        error_logs = []
        api_stats = []
        
        # 统计信息汇总
        stats = {
            "total_users": total_users,
            "total_agents": total_agents,
            "total_licenses": total_licenses,
            "active_licenses": active_licenses,
            "total_api_calls": total_api_calls,
            "today_api_calls": today_api_calls,
            "total_downloads": total_downloads,
            "today_downloads": today_downloads,
            "error_rate": error_rate,
            "memory_usage": memory_usage,
            "memory_total": memory_total,
            "memory_used": memory_used,
            "cpu_usage": cpu_usage,
            "cpu_count": cpu_count,
            "disk_usage": disk_usage,
            "disk_total": disk_total,
            "disk_used": disk_used,
            "api_trend": api_trend
        }
        
        return templates.TemplateResponse("admin/system.html", {
            "request": request,
            "uptime": uptime_str,
            "stats": stats
        })
    except Exception as e:
        logger.error(f"Error in admin system: {str(e)}", exc_info=True)
        return templates.TemplateResponse("admin/system.html", {
            "request": request,
            "uptime": "0天 0小时 0分钟",
            "stats": {
                "total_users": 0,
                "total_agents": 0,
                "total_licenses": 0,
                "active_licenses": 0,
                "total_api_calls": 0,
                "today_api_calls": 0,
                "total_downloads": 0,
                "today_downloads": 0,
                "error_rate": 0,
                "memory_usage": 0,
                "memory_total": 0,
                "memory_used": 0,
                "cpu_usage": 0,
                "cpu_count": 0,
                "disk_usage": 0,
                "disk_total": 0,
                "disk_used": 0,
                "api_trend": []
            },
            "error": f"Error loading system stats: {str(e)}"
        })

@app.get("/admin/auth-management", response_class=HTMLResponse)
async def admin_auth_management(request: Request):
    """授权管理页面"""
    return templates.TemplateResponse("admin/auth_management.html", {
        "request": request
    })

@app.get("/admin/machine-activation", response_class=HTMLResponse)
async def admin_machine_activation(request: Request):
    """机器码激活码生成页面"""
    return templates.TemplateResponse("admin/machine_activation.html", {
        "request": request
    })

@app.get("/admin/algorithm-management", response_class=HTMLResponse)
async def admin_algorithm_management(request: Request):
    """算法管理页面"""
    return templates.TemplateResponse("admin/algorithm_management.html", {
        "request": request
    })

@app.get("/admin/quick-actions", response_class=HTMLResponse)
async def admin_quick_actions(request: Request, db: Session = Depends(get_db)):
    """快速操作页面"""
    try:
        from app.models.product import Product
        from app.models.agent import Agent

        products_query = db.query(Product).filter(Product.is_active == True).all()
        agents_query = db.query(Agent).filter(Agent.is_active == True).all()

        # 转换为字典格式
        products = [
            {
                "id": p.id,
                "name": p.name,
                "price": float(p.price) if p.price else 0.0,
                "description": p.description or ""
            }
            for p in products_query
        ]

        agents = [
            {
                "id": a.id,
                "name": a.company_name,
                "username": a.username,
                "contact_name": a.contact_name or "",
                "contact_email": a.contact_email or "",
                "contact_phone": a.contact_phone or ""
            }
            for a in agents_query
        ]

        return templates.TemplateResponse("admin/quick_actions.html", {
            "request": request,
            "products": products,
            "agents": agents
        })
    except Exception as e:
        logger.error(f"Error in admin quick actions: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")



@app.get("/docs", response_class=HTMLResponse)
async def api_docs(request: Request):
    """API文档页面"""
    if not settings.enable_docs:
        raise HTTPException(status_code=404, detail="API documentation is disabled")
    return RedirectResponse(url="/docs")

@app.get("/api-docs", response_class=HTMLResponse)
async def api_documentation(request: Request):
    """API文档页面（重定向到FastAPI自动生成的文档）"""
    if not settings.enable_docs:
        raise HTTPException(status_code=404, detail="API documentation is disabled")
    return RedirectResponse(url="/docs")

@app.post("/admin/login-form")
async def admin_login_form(
    request: Request,
    username: str = Form(...),
    password: str = Form(...),
    db: Session = Depends(get_db)
):
    """管理员登录表单处理"""
    admin = AdminService.authenticate_admin(db, username, password)
    if admin:
        # 在实际应用中，这里应该设置session或cookie
        return RedirectResponse(url="/admin/dashboard", status_code=302)
    else:
        return templates.TemplateResponse(
            "admin/login.html", 
            {"request": request, "error": "用户名或密码错误"}
        )

@app.get("/admin/dashboard", response_class=HTMLResponse)
async def admin_dashboard(request: Request, db: Session = Depends(get_db)):
    """管理员仪表板"""
    try:
        # 获取统计数据
        total_users = db.query(User).count()
        active_users = db.query(User).filter(User.is_active == True).count()
        total_downloads = db.query(DownloadLog).count()
        recent_downloads = db.query(DownloadLog).order_by(DownloadLog.download_time.desc()).limit(10).all()
        
        return templates.TemplateResponse("admin/dashboard.html", {
            "request": request,
            "total_users": total_users,
            "active_users": active_users,
            "total_downloads": total_downloads,
            "recent_downloads": recent_downloads
        })
    except Exception as e:
        logger.error(f"Error in admin dashboard: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.get("/admin/users", response_class=HTMLResponse)
async def admin_users(request: Request, db: Session = Depends(get_db)):
    """用户管理页面"""
    try:
        users = db.query(User).all()
        logger.info(f"Found {len(users)} users")
        return templates.TemplateResponse("admin/users.html", {
            "request": request,
            "users": users
        })
    except Exception as e:
        logger.error(f"Error in admin users: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.get("/admin/users-simple", response_class=HTMLResponse)
async def admin_users_simple(request: Request, db: Session = Depends(get_db)):
    """简单用户管理页面测试"""
    try:
        users = db.query(User).all()
        logger.info(f"Simple test: Found {len(users)} users")
        return templates.TemplateResponse("admin/users_simple.html", {
            "request": request,
            "users": users
        })
    except Exception as e:
        logger.error(f"Error in simple users: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.get("/admin/downloads", response_class=HTMLResponse)
async def admin_downloads(request: Request, db: Session = Depends(get_db)):
    """下载统计页面"""
    try:
        downloads = db.query(DownloadLog).order_by(DownloadLog.download_time.desc()).limit(100).all()
        logger.info(f"Found {len(downloads)} download records")
        return templates.TemplateResponse("admin/downloads.html", {
            "request": request,
            "downloads": downloads
        })
    except Exception as e:
        logger.error(f"Error in admin downloads: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.get("/admin/profile", response_class=HTMLResponse)
async def admin_profile(request: Request, db: Session = Depends(get_db)):
    """管理员个人设置页面"""
    try:
        # TODO: 从session中获取当前管理员信息
        # 这里暂时使用默认管理员
        admin = db.query(AdminUser).filter(AdminUser.username == "admin").first()
        if not admin:
            raise HTTPException(status_code=404, detail="Admin not found")

        return templates.TemplateResponse("admin/profile.html", {
            "request": request,
            "admin": admin
        })
    except Exception as e:
        logger.error(f"Error in admin profile: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

# 营销页面相关路由
@app.get("/buy", response_class=HTMLResponse)
async def buy_page(request: Request):
    """购买页面 - 重定向到代理商或联系页面"""
    return RedirectResponse(url="/agent/login")

@app.get("/contact", response_class=HTMLResponse)
async def contact_page(request: Request):
    """联系我们页面"""
    return templates.TemplateResponse("contact.html", {"request": request})

@app.get("/trial", response_class=HTMLResponse)
async def trial_page(request: Request):
    """一个月免费试用页面"""
    return templates.TemplateResponse("trial.html", {"request": request})

@app.get("/features", response_class=HTMLResponse)
async def features_page(request: Request):
    """功能特色页面 - 重定向到首页功能区域"""
    return RedirectResponse(url="/#features")

@app.get("/pricing", response_class=HTMLResponse)
async def pricing_page(request: Request):
    """价格方案页面 - 重定向到首页价格区域"""
    return RedirectResponse(url="/#pricing")

@app.get("/testimonials", response_class=HTMLResponse)
async def testimonials_page(request: Request):
    """用户评价页面 - 重定向到首页评价区域"""
    return RedirectResponse(url="/#testimonials")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8008)
