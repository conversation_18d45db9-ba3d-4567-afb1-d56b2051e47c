<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}FocuSee 管理系统{% endblock %}</title>
    
    <!-- Element Plus CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css" />
    
    <!-- Vue 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    
    <!-- Element Plus JS -->
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    
    <!-- Element Plus Icons -->
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>
    
    <!-- Chart.js for statistics -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        body {
            margin: 0;
            font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
        }
        .layout-container {
            height: 100vh;
        }
        .header {
            background-color: #409eff;
            color: white;
            display: flex;
            align-items: center;
            padding: 0 20px;
        }
        .logo {
            font-size: 20px;
            font-weight: bold;
            margin-right: 20px;
        }
        .main-content {
            padding: 20px;
            background-color: #f5f7fa;
            min-height: calc(100vh - 60px);
            transition: margin-left 0.3s ease;
        }
        .sidebar {
            background-color: #304156;
            transition: width 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        .sidebar.collapsed {
            width: 64px !important;
        }
        .sidebar .el-menu {
            border-right: none;
        }
        .sidebar.collapsed .el-menu {
            width: 64px;
        }
        .sidebar.collapsed .el-menu .el-menu-item span {
            display: none;
        }
        .sidebar.collapsed .el-menu .el-menu-item {
            padding: 0 20px;
            text-align: center;
        }
        .sidebar-toggle {
            position: absolute;
            top: 10px;
            right: -15px;
            width: 30px;
            height: 30px;
            background-color: #409eff;
            border: 2px solid #fff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 1000;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }
        .sidebar-toggle:hover {
            background-color: #337ecc;
            transform: scale(1.1);
        }
        .sidebar-toggle .el-icon {
            color: white;
            font-size: 14px;
            transition: transform 0.3s ease;
        }
        .sidebar.collapsed .sidebar-toggle .el-icon {
            transform: rotate(180deg);
        }
        .content-card {
            background: white;
            border-radius: 4px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .page-header {
            margin-bottom: 20px;
        }
        .page-title {
            font-size: 24px;
            font-weight: bold;
            color: #303133;
            margin: 0;
        }
        /* 快速操作侧边栏样式 */
        .quick-action-fab {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: linear-gradient(135deg, #409eff, #67c23a);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 1001;
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
            transition: all 0.3s ease;
        }
        .quick-action-fab:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 16px rgba(64, 158, 255, 0.6);
        }
        .quick-action-fab .el-icon {
            color: white;
            font-size: 24px;
            transition: transform 0.3s ease;
        }
        .quick-action-fab.active .el-icon {
            transform: rotate(45deg);
        }

        .quick-action-drawer {
            position: fixed;
            top: 0;
            right: 0;
            width: 100%;
            max-width: 400px;
            height: 100vh;
            background: white;
            box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);
            transform: translateX(100%);
            transition: transform 0.3s ease;
            z-index: 1000;
            overflow-y: auto;
        }
        .quick-action-drawer.open {
            transform: translateX(0);
        }

        .quick-action-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }
        .quick-action-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .quick-action-header {
            padding: 20px;
            background: linear-gradient(135deg, #409eff, #67c23a);
            color: white;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .quick-action-content {
            padding: 20px;
        }
        .quick-action-item {
            margin-bottom: 16px;
            padding: 16px;
            border: 1px solid #e4e7ed;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }
        .quick-action-item:hover {
            border-color: #409eff;
            box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
            transform: translateY(-2px);
        }
        .quick-action-item .icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 12px;
            color: white;
        }
        .quick-action-item .title {
            font-size: 16px;
            font-weight: bold;
            color: #303133;
            margin-bottom: 8px;
        }
        .quick-action-item .description {
            font-size: 14px;
            color: #606266;
            line-height: 1.4;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                width: 64px !important;
            }
            .sidebar .el-menu .el-menu-item span {
                display: none;
            }
            .sidebar .el-menu .el-menu-item {
                padding: 0 20px;
                text-align: center;
            }
            .sidebar-toggle {
                display: none;
            }
            .quick-action-drawer {
                max-width: 100%;
            }
            .quick-action-fab {
                bottom: 16px;
                right: 16px;
                width: 48px;
                height: 48px;
            }
            .quick-action-fab .el-icon {
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <el-container class="layout-container">
            <!-- 头部 -->
            <el-header class="header" height="60px">
                <div class="logo">
                    <el-icon><Monitor /></el-icon>
                    FocuSee 管理系统
                </div>
                <div style="flex: 1;"></div>
                <el-dropdown>
                    <span class="el-dropdown-link" style="color: white; cursor: pointer;">
                        <el-icon><User /></el-icon>
                        管理员
                        <el-icon class="el-icon--right"><arrow-down /></el-icon>
                    </span>
                    <template #dropdown>
                        <el-dropdown-menu>
                            <el-dropdown-item @click="goToProfile">个人设置</el-dropdown-item>
                            <el-dropdown-item divided @click="logout">退出登录</el-dropdown-item>
                        </el-dropdown-menu>
                    </template>
                </el-dropdown>
            </el-header>
            
            <el-container>
                <!-- 侧边栏 -->
                <el-aside class="sidebar" :class="{ collapsed: sidebarCollapsed }" :width="sidebarCollapsed ? '64px' : '200px'">
                    <!-- 收缩按钮 -->
                    <div class="sidebar-toggle" @click="toggleSidebar" :title="sidebarCollapsed ? '展开菜单' : '收缩菜单'">
                        <el-icon><ArrowLeft /></el-icon>
                    </div>

                    <el-menu
                        :default-active="activeMenu"
                        class="el-menu-vertical"
                        background-color="#304156"
                        text-color="#bfcbd9"
                        active-text-color="#409eff"
                        :collapse="sidebarCollapsed">
                        <el-menu-item index="/admin/dashboard" @click="navigate('/admin/dashboard')">
                            <el-icon><Odometer /></el-icon>
                            <span>仪表板</span>
                        </el-menu-item>
                        <el-menu-item index="/admin/quick-actions" @click="navigate('/admin/quick-actions')">
                            <el-icon><Lightning /></el-icon>
                            <span>快速操作</span>
                        </el-menu-item>
                        <el-menu-item index="/admin/users" @click="navigate('/admin/users')">
                            <el-icon><User /></el-icon>
                            <span>用户管理</span>
                        </el-menu-item>
                        <el-menu-item index="/admin/products" @click="navigate('/admin/products')">
                            <el-icon><Box /></el-icon>
                            <span>产品管理</span>
                        </el-menu-item>
                        <el-menu-item index="/admin/orders" @click="navigate('/admin/orders')">
                            <el-icon><Document /></el-icon>
                            <span>订单管理</span>
                        </el-menu-item>
                        <el-menu-item index="/admin/licenses" @click="navigate('/admin/licenses')">
                            <el-icon><Key /></el-icon>
                            <span>授权码管理</span>
                        </el-menu-item>
                        <el-menu-item index="/admin/auth-management" @click="navigate('/admin/auth-management')">
                            <el-icon><Lock /></el-icon>
                            <span>授权管理</span>
                        </el-menu-item>
                        <el-sub-menu index="machine-codes">
                            <template #title>
                                <el-icon><Cpu /></el-icon>
                                <span>机器码管理</span>
                            </template>
                            <el-menu-item index="/admin/machine-activation" @click="navigate('/admin/machine-activation')">
                                <el-icon><Key /></el-icon>
                                <span>激活码管理</span>
                            </el-menu-item>
                            <el-menu-item index="/admin/algorithm-management" @click="navigate('/admin/algorithm-management')">
                                <el-icon><Setting /></el-icon>
                                <span>算法管理</span>
                            </el-menu-item>
                        </el-sub-menu>
                        <el-menu-item index="/admin/agents" @click="navigate('/admin/agents')">
                            <el-icon><Avatar /></el-icon>
                            <span>代理商管理</span>
                        </el-menu-item>
                        <el-menu-item index="/admin/system" @click="navigate('/admin/system')">
                            <el-icon><Monitor /></el-icon>
                            <span>系统监控</span>
                        </el-menu-item>
                        <el-menu-item index="/admin/downloads" @click="navigate('/admin/downloads')">
                            <el-icon><Download /></el-icon>
                            <span>下载统计</span>
                        </el-menu-item>
                    </el-menu>
                </el-aside>
                
                <!-- 主内容区 -->
                <el-main class="main-content">
                    <div class="page-header">
                        <h1 class="page-title">{% block page_title %}管理面板{% endblock %}</h1>
                    </div>
                    {% block content %}{% endblock %}
                </el-main>
            </el-container>
        </el-container>

        <!-- 快速操作浮动按钮 -->
        <div class="quick-action-fab" :class="{ active: quickActionOpen }" @click="toggleQuickAction" title="快速操作">
            <el-icon><Plus /></el-icon>
        </div>

        <!-- 快速操作遮罩层 -->
        <div class="quick-action-overlay" :class="{ show: quickActionOpen }" @click="closeQuickAction"></div>

        <!-- 快速操作侧边栏 -->
        <div class="quick-action-drawer" :class="{ open: quickActionOpen }">
            <div class="quick-action-header">
                <h3 style="margin: 0; font-size: 18px;">
                    <el-icon style="margin-right: 8px;"><Lightning /></el-icon>
                    快速操作
                </h3>
                <el-button type="text" @click="closeQuickAction" style="color: white; padding: 0;">
                    <el-icon size="20"><Close /></el-icon>
                </el-button>
            </div>

            <div class="quick-action-content">
                <!-- 创建订单 -->
                <div class="quick-action-item" @click="openCreateOrderDialog">
                    <div class="icon" style="background: linear-gradient(135deg, #409eff, #337ecc);">
                        <el-icon size="20"><Document /></el-icon>
                    </div>
                    <div class="title">创建订单</div>
                    <div class="description">快速创建新的产品订单，支持选择产品、代理商和设置订单信息</div>
                </div>

                <!-- 创建授权码 -->
                <div class="quick-action-item" @click="openCreateLicenseDialog">
                    <div class="icon" style="background: linear-gradient(135deg, #67c23a, #529b2e);">
                        <el-icon size="20"><Key /></el-icon>
                    </div>
                    <div class="title">创建授权码</div>
                    <div class="description">生成新的产品授权码，可设置有效期、使用次数等参数</div>
                </div>

                <!-- 创建机器码 -->
                <div class="quick-action-item" @click="openCreateMachineCodeDialog">
                    <div class="icon" style="background: linear-gradient(135deg, #e6a23c, #cf9236);">
                        <el-icon size="20"><Cpu /></el-icon>
                    </div>
                    <div class="title">创建机器码</div>
                    <div class="description">生成机器码激活码，用于设备绑定和离线激活</div>
                </div>

                <!-- 快速导航 -->
                <div style="margin-top: 24px; padding-top: 20px; border-top: 1px solid #e4e7ed;">
                    <h4 style="margin: 0 0 16px 0; color: #606266; font-size: 14px;">快速导航</h4>
                    <el-row :gutter="12">
                        <el-col :span="12">
                            <el-button type="primary" size="small" @click="navigate('/admin/orders')" style="width: 100%;">
                                <el-icon><Document /></el-icon>
                                订单管理
                            </el-button>
                        </el-col>
                        <el-col :span="12">
                            <el-button type="success" size="small" @click="navigate('/admin/licenses')" style="width: 100%;">
                                <el-icon><Key /></el-icon>
                                授权码
                            </el-button>
                        </el-col>
                    </el-row>
                    <el-row :gutter="12" style="margin-top: 12px;">
                        <el-col :span="12">
                            <el-button type="warning" size="small" @click="navigate('/admin/machine-activation')" style="width: 100%;">
                                <el-icon><Cpu /></el-icon>
                                机器码
                            </el-button>
                        </el-col>
                        <el-col :span="12">
                            <el-button type="info" size="small" @click="navigate('/admin/dashboard')" style="width: 100%;">
                                <el-icon><Odometer /></el-icon>
                                仪表板
                            </el-button>
                        </el-col>
                    </el-row>
                </div>
            </div>
        </div>
    </div>

    <script>
        const { createApp } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;
        
        const app = createApp({
            data() {
                return {
                    activeMenu: '{{ request.url.path }}',
                    sidebarCollapsed: localStorage.getItem('sidebarCollapsed') === 'true' || false,
                    quickActionOpen: false,
                    {% block data %}{% endblock %}
                };
            },
            computed: {
                {% block computed %}{% endblock %}
            },
            methods: {
                navigate(path) {
                    window.location.href = path;
                },
                formatTime(timeStr) {
                    if (!timeStr) return '';
                    const date = new Date(timeStr);
                    return date.toLocaleString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit'
                    });
                },
                goToProfile() {
                    window.location.href = '/admin/profile';
                },
                logout() {
                    ElMessageBox.confirm('确定要退出登录吗？', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        window.location.href = '/';
                    });
                },
                toggleSidebar() {
                    this.sidebarCollapsed = !this.sidebarCollapsed;
                    // 保存状态到localStorage
                    localStorage.setItem('sidebarCollapsed', this.sidebarCollapsed.toString());
                },
                toggleQuickAction() {
                    this.quickActionOpen = !this.quickActionOpen;
                },
                closeQuickAction() {
                    this.quickActionOpen = false;
                },
                openCreateOrderDialog() {
                    this.closeQuickAction();
                    this.showCreateOrderDialog();
                },
                openCreateLicenseDialog() {
                    this.closeQuickAction();
                    this.showCreateLicenseDialog();
                },
                openCreateMachineCodeDialog() {
                    this.closeQuickAction();
                    this.showCreateMachineCodeDialog();
                },
                showCreateOrderDialog() {
                    // 跳转到快速操作页面
                    window.location.href = '/admin/quick-actions?action=order';
                },
                showCreateLicenseDialog() {
                    // 跳转到快速操作页面
                    window.location.href = '/admin/quick-actions?action=license';
                },
                showCreateMachineCodeDialog() {
                    // 跳转到快速操作页面
                    window.location.href = '/admin/quick-actions?action=machine';
                },
                {% block methods %}{% endblock %}
            },
            mounted() {
                // 监听ESC键关闭快速操作侧边栏
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'Escape' && this.quickActionOpen) {
                        this.closeQuickAction();
                    }
                });
                {% block mounted %}{% endblock %}
            }
        });
        
        // 注册 Element Plus 图标
        for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
            app.component(key, component);
        }
        
        app.use(ElementPlus);
        app.mount('#app');
    </script>
    
    {% block scripts %}{% endblock %}
</body>
</html>
