from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel
from app.database import get_db
from app.models import User
from app.services.admin_service import AdminService
from app.utils.password import verify_password
import logging

logger = logging.getLogger(__name__)

router = APIRouter()

class UserLogin(BaseModel):
    username: str
    password: str

class UserRegister(BaseModel):
    username: str
    password: str
    email: str
    phone: str = None

class UserProfileUpdate(BaseModel):
    email: str = None
    phone: str = None
    full_name: str = None

@router.post("/register")
async def user_register(
    register_data: UserRegister,
    db: Session = Depends(get_db)
):
    """用户注册"""
    try:
        # 检查用户名是否已存在
        existing_user = db.query(User).filter(User.username == register_data.username).first()
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Username already exists"
            )
        
        # 检查邮箱是否已存在
        existing_email = db.query(User).filter(User.email == register_data.email).first()
        if existing_email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already exists"
            )
        
        # 创建新用户
        from app.utils.password import hash_password
        user = User(
            username=register_data.username,
            password_hash=hash_password(register_data.password),
            email=register_data.email,
            phone=register_data.phone,
            user_type="user",
            is_active=True
        )
        
        db.add(user)
        db.commit()
        db.refresh(user)
        
        logger.info(f"User {user.username} registered successfully")
        
        return {
            "message": "User registered successfully",
            "user_id": user.id,
            "username": user.username
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error during user registration: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.post("/login")
async def user_login(
    login_data: UserLogin,
    db: Session = Depends(get_db)
):
    """用户登录"""
    try:
        # 验证用户凭据
        user = db.query(User).filter(User.username == login_data.username).first()
        if not user or not verify_password(login_data.password, user.password_hash):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid username or password"
            )
        
        if not user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Account is disabled"
            )
        
        # 生成JWT token
        token = AdminService.create_access_token(data={"sub": f"user:{user.username}", "user_id": user.id})
        
        logger.info(f"User {user.username} logged in successfully")
        
        return {
            "access_token": token,
            "token_type": "bearer",
            "user_info": {
                "id": user.id,
                "username": user.username,
                "email": user.email,
                "phone": user.phone,
                "full_name": user.full_name,
                "user_type": user.user_type
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error during user login: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.post("/logout")
async def user_logout():
    """用户登出"""
    # 由于使用JWT，登出主要在客户端处理（删除token）
    return {"message": "Logged out successfully"}

@router.get("/me")
async def get_current_user(
    db: Session = Depends(get_db)
    # TODO: 添加JWT token验证
):
    """获取当前用户信息"""
    # TODO: 从JWT token中获取用户ID
    user_id = 1  # 临时硬编码
    
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    return {
        "id": user.id,
        "username": user.username,
        "email": user.email,
        "phone": user.phone,
        "full_name": user.full_name,
        "user_type": user.user_type,
        "is_active": user.is_active,
        "created_at": user.created_at
    }

@router.put("/profile")
async def update_user_profile(
    profile_data: UserProfileUpdate,
    db: Session = Depends(get_db)
    # TODO: 添加JWT token验证
):
    """更新用户个人信息"""
    # TODO: 从JWT token中获取用户ID
    user_id = 1  # 临时硬编码
    
    try:
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # 更新用户信息
        if profile_data.email is not None:
            # 检查邮箱是否已被其他用户使用
            existing_email = db.query(User).filter(
                User.email == profile_data.email,
                User.id != user_id
            ).first()
            if existing_email:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Email already exists"
                )
            user.email = profile_data.email
        
        if profile_data.phone is not None:
            user.phone = profile_data.phone
        
        if profile_data.full_name is not None:
            user.full_name = profile_data.full_name
        
        db.commit()
        db.refresh(user)
        
        logger.info(f"User {user.username} profile updated successfully")
        
        return {
            "message": "Profile updated successfully",
            "user_info": {
                "id": user.id,
                "username": user.username,
                "email": user.email,
                "phone": user.phone,
                "full_name": user.full_name,
                "user_type": user.user_type
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating user profile: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.put("/password")
async def change_password(
    old_password: str,
    new_password: str,
    db: Session = Depends(get_db)
    # TODO: 添加JWT token验证
):
    """修改密码"""
    # TODO: 从JWT token中获取用户ID
    user_id = 1  # 临时硬编码
    
    try:
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # 验证旧密码
        if not verify_password(old_password, user.password_hash):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid old password"
            )
        
        # 更新密码
        from app.utils.password import hash_password
        user.password_hash = hash_password(new_password)
        
        db.commit()
        
        logger.info(f"User {user.username} password changed successfully")
        
        return {"message": "Password changed successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error changing password: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )
