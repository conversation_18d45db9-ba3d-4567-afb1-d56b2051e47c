#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新默认算法代码，移除import语句
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import sessionmaker
from app.database import engine
from app.models.activation_algorithm import ActivationAlgorithm
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def update_default_algorithm():
    """更新默认算法代码"""
    try:
        # 创建会话
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()
        
        try:
            # 查找默认算法
            default_algorithm = db.query(ActivationAlgorithm).filter(
                ActivationAlgorithm.is_default == True
            ).first()
            
            if not default_algorithm:
                logger.error("未找到默认算法")
                return False
            
            # 新的算法代码（移除import语句）
            new_algorithm_code = '''# HMAC-SHA256 激活码生成算法
# 这是系统默认使用的算法，基于HMAC-SHA256和Base64编码
# 注意：hashlib, hmac, time, base64 模块已经在执行环境中可用，无需导入

# 使用与原系统相同的密钥
master_key = "FocuSee_Master_2025_Secret_Key_V1"
hmac_key = hashlib.sha256((master_key + "_HMAC").encode()).digest()[:32]

try:
    current_time = int(time.time())
    machine_short = machine_id.upper().strip()[:8]
    time_short = str(current_time)[-4:]

    # 构造数据：机器码|天数|时间戳
    compact_data = f"{machine_short}|{days}|{time_short}"

    # 计算校验码
    hmac_hash = hmac.new(hmac_key, compact_data.encode(), hashlib.sha256).hexdigest()[:8]

    # 最终数据
    final_data = compact_data + "|" + hmac_hash

    # Base64编码并格式化
    encoded = base64.b64encode(final_data.encode()).decode().rstrip('=')
    activation_code = '-'.join([encoded[i:i+4] for i in range(0, len(encoded), 4)])

except Exception as e:
    activation_code = None'''
            
            # 更新算法代码
            default_algorithm.algorithm_code = new_algorithm_code
            db.commit()
            
            logger.info(f"默认算法代码更新成功: {default_algorithm.name}")
            return True
            
        except Exception as e:
            logger.error(f"更新默认算法失败: {str(e)}")
            db.rollback()
            return False
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"数据库操作失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = update_default_algorithm()
    if success:
        print("✅ 默认算法代码更新成功！")
    else:
        print("❌ 默认算法代码更新失败！")
        sys.exit(1)
