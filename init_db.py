#!/usr/bin/env python3
"""
数据库初始化脚本
"""

import pymysql
from sqlalchemy import create_engine
from app.database import create_tables, SessionLocal
from app.models import User, AdminUser, DownloadLog
from app.services.admin_service import AdminService
from app.config import settings
import logging
import re

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_database_if_not_exists():
    """创建数据库（如果不存在）"""
    try:
        # 解析数据库URL
        url_pattern = r'mysql\+pymysql://([^:]+):([^@]+)@([^:]+):(\d+)/(.+)'
        match = re.match(url_pattern, settings.database_url)

        if not match:
            raise ValueError("Invalid database URL format")

        username, password, host, port, database = match.groups()
        port = int(port)

        # 连接到MySQL服务器（不指定数据库）
        connection = pymysql.connect(
            host=host,
            port=port,
            user=username,
            password=password,
            charset='utf8mb4'
        )

        try:
            with connection.cursor() as cursor:
                # 检查数据库是否存在
                cursor.execute(f"SHOW DATABASES LIKE '{database}'")
                result = cursor.fetchone()

                if not result:
                    # 创建数据库
                    logger.info(f"Creating database '{database}'...")
                    cursor.execute(f"CREATE DATABASE {database} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
                    logger.info(f"Database '{database}' created successfully!")
                else:
                    logger.info(f"Database '{database}' already exists")

            connection.commit()

        finally:
            connection.close()

    except Exception as e:
        logger.error(f"Error creating database: {str(e)}")
        raise

def init_database():
    """初始化数据库"""
    try:
        # 首先创建数据库
        create_database_if_not_exists()

        # 创建所有表
        logger.info("Creating database tables...")
        create_tables()
        logger.info("Database tables created successfully!")
        
        # 创建默认管理员账户
        db = SessionLocal()
        try:
            existing_admin = db.query(AdminUser).filter(AdminUser.username == settings.admin_username).first()
            if not existing_admin:
                admin = AdminService.create_admin_user(db, settings.admin_username, settings.admin_password)
                logger.info(f"Created default admin user: {settings.admin_username}")
            else:
                logger.info("Default admin user already exists")
                
            # 创建示例用户（可选）
            existing_user = db.query(User).filter(User.user_id == "demo_user").first()
            if not existing_user:
                demo_user = User(
                    user_id="demo_user",
                    license_key="demo_license_123",
                    description="演示用户",
                    is_active=True
                )
                db.add(demo_user)
                db.commit()
                logger.info("Created demo user: demo_user")
            else:
                logger.info("Demo user already exists")
                
        except Exception as e:
            logger.error(f"Error creating default data: {str(e)}")
            db.rollback()
        finally:
            db.close()
            
        logger.info("Database initialization completed!")
        
    except Exception as e:
        logger.error(f"Database initialization failed: {str(e)}")
        raise

if __name__ == "__main__":
    init_database()
