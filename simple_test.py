#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("开始测试...")

try:
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    print("路径设置完成")
    
    from app.database import engine
    print("数据库引擎导入成功")
    
    from sqlalchemy.orm import sessionmaker
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    print("数据库会话创建成功")
    
    from app.models.activation_algorithm import ActivationAlgorithm
    print("模型导入成功")
    
    algorithms = db.query(ActivationAlgorithm).all()
    print(f"查询到 {len(algorithms)} 个算法")
    
    for algorithm in algorithms:
        print(f"算法: {algorithm.name}")
    
    db.close()
    print("测试完成")
    
except Exception as e:
    print(f"错误: {str(e)}")
    import traceback
    traceback.print_exc()
