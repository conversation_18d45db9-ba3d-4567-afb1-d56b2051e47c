from pydantic_settings import BaseSettings
from typing import Optional
import os
from dotenv import load_dotenv

load_dotenv()

class Settings(BaseSettings):
    # 数据库配置
    database_url: str = os.getenv("DATABASE_URL", "mysql+pymysql://root:123456@localhost:3306/focusee_db")
    
    # 安全配置
    secret_key: str = os.getenv("SECRET_KEY", "your-secret-key-here-change-in-production")
    algorithm: str = os.getenv("ALGORITHM", "HS256")
    access_token_expire_minutes: int = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30"))
    
    # 应用配置
    app_name: str = os.getenv("APP_NAME", "FocuSee Registration System")
    app_version: str = os.getenv("APP_VERSION", "1.0.0")
    debug: bool = os.getenv("DEBUG", "True").lower() == "true"

    # API 文档配置
    enable_docs: bool = os.getenv("ENABLE_DOCS", "True").lower() == "true"
    docs_url: Optional[str] = "/docs" if os.getenv("ENABLE_DOCS", "True").lower() == "true" else None
    redoc_url: Optional[str] = "/redoc" if os.getenv("ENABLE_DOCS", "True").lower() == "true" else None
    
    # 文件存储配置
    downloads_dir: str = os.getenv("DOWNLOADS_DIR", "./downloads")
    max_file_size: int = int(os.getenv("MAX_FILE_SIZE", "100000000"))
    
    # 管理员默认账户
    admin_username: str = os.getenv("ADMIN_USERNAME", "admin")
    admin_password: str = os.getenv("ADMIN_PASSWORD", "admin123")
    
    class Config:
        env_file = ".env"

settings = Settings()
